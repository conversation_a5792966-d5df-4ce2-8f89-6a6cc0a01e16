# Generated by Django 5.1.7 on 2025-03-22 17:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cutting', '0001_initial'),
        ('fabric', '0004_remove_fabric_category_remove_fabric_supplier_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='cuttingrecord',
            old_name='date',
            new_name='cutting_date',
        ),
        migrations.RemoveField(
            model_name='cuttingrecord',
            name='total_color',
        ),
        migrations.RemoveField(
            model_name='cuttingrecord',
            name='total_quantity',
        ),
        migrations.RemoveField(
            model_name='cuttingrecord',
            name='total_size',
        ),
        migrations.RemoveField(
            model_name='cuttingrecordfabric',
            name='cuttingrecord',
        ),
        migrations.RemoveField(
            model_name='cuttingrecordfabric',
            name='quantity_cut',
        ),
        migrations.RemoveField(
            model_name='cuttingrecordfabric',
            name='size',
        ),
        migrations.AddField(
            model_name='cuttingrecord',
            name='fabric_definition',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='fabric.fabricdefinition'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='cuttingrecordfabric',
            name='cutting_record',
            field=models.ForeignKey(default=123, on_delete=django.db.models.deletion.CASCADE, related_name='details', to='cutting.cuttingrecord'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='cuttingrecordfabric',
            name='l',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='cuttingrecordfabric',
            name='m',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='cuttingrecordfabric',
            name='s',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='cuttingrecordfabric',
            name='xl',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='cuttingrecordfabric',
            name='xs',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='cuttingrecordfabric',
            name='yard_usage',
            field=models.DecimalField(decimal_places=2, help_text='Total yards used for this variant', max_digits=10),
        ),
    ]
