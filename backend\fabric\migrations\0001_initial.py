# Generated by Django 5.1.7 on 2025-03-10 09:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('supplier_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('address', models.TextField()),
                ('tel_no', models.CharField(max_length=15)),
            ],
        ),
        migrations.CreateModel(
            name='Fabric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('date_added', models.DateTimeField(auto_now_add=True)),
                ('color', models.CharField(max_length=50)),
                ('total_yard', models.FloatField()),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fabrics', to='fabric.supplier')),
            ],
        ),
    ]
