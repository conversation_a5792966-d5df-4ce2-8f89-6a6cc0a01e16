# Generated by Django 5.1.7 on 2025-03-17 17:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fabric', '0002_fabric_price_per_yard'),
    ]

    operations = [
        migrations.CreateModel(
            name='FabricCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.AddField(
            model_name='fabric',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fabrics', to='fabric.fabriccategory'),
        ),
    ]
