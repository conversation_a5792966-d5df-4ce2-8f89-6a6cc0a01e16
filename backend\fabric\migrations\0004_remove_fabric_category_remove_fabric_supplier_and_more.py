# Generated by Django 5.1.7 on 2025-03-17 18:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fabric', '0003_fabriccategory_fabric_category'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='fabric',
            name='category',
        ),
        migrations.RemoveField(
            model_name='fabric',
            name='supplier',
        ),
        migrations.CreateModel(
            name='FabricDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fabric_name', models.CharField(max_length=100)),
                ('date_added', models.DateField()),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fabric.supplier')),
            ],
        ),
        migrations.CreateModel(
            name='FabricVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('color', models.CharField(max_length=50)),
                ('total_yard', models.FloatField()),
                ('price_per_yard', models.FloatField()),
                ('fabric_definition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='fabric.fabricdefinition')),
            ],
        ),
        migrations.DeleteModel(
            name='FabricCategory',
        ),
        migrations.DeleteModel(
            name='Fabric',
        ),
    ]
