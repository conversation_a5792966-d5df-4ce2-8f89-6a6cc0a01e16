# Generated by Django 5.1.7 on 2025-03-30 17:09

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cutting', '0003_cuttingrecord_product_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='FinishedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_sewn_xs', models.IntegerField(default=0)),
                ('total_sewn_s', models.IntegerField(default=0)),
                ('total_sewn_m', models.IntegerField(default=0)),
                ('total_sewn_l', models.IntegerField(default=0)),
                ('total_sewn_xl', models.IntegerField(default=0)),
                ('manufacture_price', models.FloatField()),
                ('selling_price', models.FloatField()),
                ('approval_date', models.DateField(default=django.utils.timezone.now)),
                ('cutting_record', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='finished_product', to='cutting.cuttingrecord')),
            ],
        ),
    ]
