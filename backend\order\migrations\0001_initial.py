# Generated by Django 5.1.7 on 2025-04-17 07:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('finished_product', '0003_finishedproduct_available_quantity'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Shop',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('address', models.TextField()),
                ('contact_number', models.Char<PERSON>ield(max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('submitted', 'Submitted'), ('approved', 'Approved'), ('invoiced', 'Invoiced'), ('delivered', 'Delivered')], default='draft', max_length=20)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('invoice_number', models.CharField(blank=True, max_length=50)),
                ('placed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('shop', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='order.shop')),
            ],
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_6_packs', models.PositiveIntegerField(default=0)),
                ('quantity_12_packs', models.PositiveIntegerField(default=0)),
                ('quantity_extra_items', models.PositiveIntegerField(default=0)),
                ('finished_product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='finished_product.finishedproduct')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='order.order')),
            ],
        ),
    ]
