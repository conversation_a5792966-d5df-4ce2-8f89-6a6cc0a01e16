# Generated by Django 5.1.6 on 2025-04-04 14:22

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('finished_product', '0002_finishedproduct_is_provisional_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PackingDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number_of_6_packs', models.IntegerField(default=0)),
                ('number_of_12_packs', models.IntegerField(default=0)),
                ('extra_items', models.IntegerField(default=0)),
                ('total_packed_quantity', models.IntegerField(default=0)),
                ('finished_product', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='finished_product.finishedproduct')),
            ],
        ),
        migrations.CreateModel(
            name='PackingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=datetime.date.today)),
                ('number_of_6_packs', models.IntegerField(default=0)),
                ('number_of_12_packs', models.IntegerField(default=0)),
                ('extra_items', models.IntegerField(default=0)),
                ('total_packed_quantity', models.IntegerField()),
                ('finished_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='finished_product.finishedproduct')),
            ],
        ),
    ]
