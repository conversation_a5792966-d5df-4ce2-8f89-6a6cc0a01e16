# Generated by Django 5.1.7 on 2025-03-24 08:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('fabric', '0005_fabricvariant_color_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='DailySewingRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(auto_now_add=True)),
                ('xs', models.IntegerField(default=0)),
                ('s', models.IntegerField(default=0)),
                ('m', models.IntegerField(default=0)),
                ('l', models.IntegerField(default=0)),
                ('xl', models.IntegerField(default=0)),
                ('damage_count', models.IntegerField(default=0)),
                ('fabric_variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fabric.fabricvariant')),
            ],
        ),
        migrations.CreateModel(
            name='FinishedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_sewn_xs', models.IntegerField(default=0)),
                ('total_sewn_s', models.IntegerField(default=0)),
                ('total_sewn_m', models.IntegerField(default=0)),
                ('total_sewn_l', models.IntegerField(default=0)),
                ('total_sewn_xl', models.IntegerField(default=0)),
                ('damage_count', models.IntegerField(default=0)),
                ('last_update_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(default='In Progress', max_length=20)),
                ('fabric_variant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='fabric.fabricvariant')),
            ],
        ),
    ]
