# Generated by Django 5.1.7 on 2025-03-30 10:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cutting', '0003_cuttingrecord_product_name'),
        ('sewing', '0002_alter_dailysewingrecord_fabric_variant_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dailysewingrecord',
            name='fabric_variant',
        ),
        migrations.AddField(
            model_name='dailysewingrecord',
            name='cutting_detail',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='daily_sewing_records', to='cutting.cuttingrecordfabric'),
            preserve_default=False,
        ),
        migrations.DeleteModel(
            name='FinishedProduct',
        ),
    ]
