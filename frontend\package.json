{"name": "frontend", "version": "0.1.0", "private": true, "homepage": "./", "dependencies": {"@react-google-maps/api": "^2.20.6", "@shadcn/ui": "^0.0.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "firebase": "^11.6.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.484.0", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-google-autocomplete": "^2.7.5", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "react-select": "^5.10.1", "recharts": "^2.12.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}