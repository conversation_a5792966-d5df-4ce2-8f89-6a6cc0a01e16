/* ProductImageUploader.css */

.product-image-uploader {
  margin-bottom: 2rem;
}

.image-upload-container {
  border: 2px dashed #ccc;
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.image-upload-container:hover {
  border-color: #0d6efd;
  background-color: #f0f7ff;
}

.image-upload-container.active {
  border-color: #0d6efd;
  background-color: #e6f2ff;
}

.image-preview-container {
  margin-top: 1rem;
}

.image-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
}

.image-preview-item {
  position: relative;
  border-radius: 5px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.image-preview-item.active {
  border-color: #0d6efd;
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  height: 100px;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-preview-item:hover .image-actions {
  opacity: 1;
}

.btn-remove-image {
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.image-number {
  position: absolute;
  bottom: 5px;
  left: 5px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
}

.upload-progress-container {
  border-radius: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .image-preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
  
  .preview-image {
    height: 80px;
  }
}
