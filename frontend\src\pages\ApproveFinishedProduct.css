/* ApproveFinishedProduct.css */

/* Card styling */
.product-card {
  transition: all 0.3s ease;
  border-radius: 10px;
  overflow: hidden;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Form styling */
.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Image upload area */
.image-upload-container {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-upload-container:hover {
  border-color: #80bdff;
  background-color: rgba(0, 123, 255, 0.05);
}

.image-upload-container.active {
  border-color: #28a745;
  background-color: rgba(40, 167, 69, 0.05);
}

/* Image preview */
.image-preview {
  max-height: 200px;
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.image-preview:hover {
  transform: scale(1.05);
}

/* Color swatches */
.color-swatch {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 10px;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-swatch:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.color-swatch.active {
  border: 2px solid #007bff;
  transform: scale(1.2);
}

/* Size quantity visualization */
.size-quantity-bar {
  height: 30px;
  border-radius: 15px;
  margin-bottom: 10px;
  transition: width 0.5s ease;
}

/* Profit margin visualization */
.profit-margin-indicator {
  height: 8px;
  border-radius: 4px;
  margin-top: 5px;
  background: linear-gradient(to right, #dc3545, #ffc107, #28a745);
}

/* Animation effects */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .image-preview {
    max-height: 150px;
  }

  .color-swatch {
    width: 25px;
    height: 25px;
  }
}

/* Button styling */
.btn-approve {
  background: linear-gradient(45deg, #28a745, #20c997);
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.btn-approve:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(45deg, #218838, #1e9e7f);
}

/* Tab styling */
.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
  padding: 10px 15px;
  transition: all 0.2s ease;
}

.nav-tabs .nav-link.active {
  color: #007bff;
  border-bottom: 2px solid #007bff;
  background-color: transparent;
}

/* Tooltip styling */
.custom-tooltip {
  max-width: 200px;
  font-size: 0.8rem;
}

/* Modal styling */
.confirmation-modal .modal-content {
  border-radius: 10px;
  overflow: hidden;
}

.confirmation-modal .modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.confirmation-modal .modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

/* Multiple image display styling */
.product-images-container {
  margin-bottom: 20px;
}

.product-images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.product-image-item {
  position: relative;
  width: 80px;
  height: 80px;
  border: 2px solid #dee2e6;
  border-radius: 5px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.product-image-item.active {
  border-color: #0d6efd;
  transform: scale(1.05);
}

.product-image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-number {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 6px;
  font-size: 0.7rem;
  border-top-left-radius: 5px;
}

.main-image-container {
  text-align: center;
  margin-top: 15px;
}

.main-image-preview {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
}

.image-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.product-image-item:hover .image-actions {
  opacity: 1;
}

.btn-remove-image {
  padding: 2px 5px;
  font-size: 0.7rem;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
