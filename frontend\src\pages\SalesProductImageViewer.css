/* SalesProductImageViewer.css */

/* Page Layout */
.page-header {
    margin-bottom: 2rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}

.product-gallery {
    padding: 1rem 0;
}

/* Product Card Styling */
.product-image-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
}

.product-image-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.image-container {
    position: relative;
    height: 180px;
    overflow: hidden;
    background-color: #f8f9fa;
}

.card-img-top {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-image-card:hover .card-img-top {
    transform: scale(1.1);
}

.stock-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.no-image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #adb5bd;
    background-color: #f8f9fa;
}

.no-image-placeholder p {
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.product-title {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.price-tag {
    font-weight: bold;
    color: #0d6efd;
    margin-bottom: 0.5rem;
}

.view-more-btn {
    width: 100%;
    font-size: 0.8rem;
    padding: 0.25rem;
}

/* Modal Styling */
.product-detail-modal .modal-content {
    border-radius: 15px;
    overflow: hidden;
}

.modal-image-container {
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.product-detail-image {
    max-height: 100%;
    object-fit: contain;
}

.carousel-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 350px;
    background-color: #f8f9fa;
}

.image-counter {
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-bottom: 10px;
}

.carousel-control-prev,
.carousel-control-next {
    width: 10%;
    opacity: 0.7;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-indicators {
    margin-bottom: 0;
}

.carousel-indicators button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0 5px;
}

.no-image-placeholder.large {
    height: 350px;
    border-radius: 10px;
    background-color: #f8f9fa;
}

.product-info {
    padding: 1rem;
}

.product-description {
    margin-bottom: 1.5rem;
    color: #6c757d;
}

.price-section, .inventory-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 10px;
}

.price-tag-large {
    font-size: 1.5rem;
    font-weight: bold;
    color: #0d6efd;
}

.stock-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stock-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.stock-item:last-child {
    border-bottom: none;
}

.stock-item.total {
    font-weight: bold;
    margin-top: 0.5rem;
    border-top: 2px solid #dee2e6;
    border-bottom: none;
}

.navigation-buttons {
    display: flex;
    gap: 1rem;
    margin-right: auto;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background-color: #f8f9fa;
    border-radius: 10px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .image-container {
        height: 150px;
    }

    .modal-image-container {
        height: 250px;
    }

    .navigation-buttons {
        width: 100%;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .product-detail-modal .modal-footer {
        flex-direction: column;
    }

    .product-detail-modal .modal-footer button {
        width: 100%;
    }
}
