/* SalesProductView.css */

/* Product Card Styling */
.product-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 8px;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.product-image-container {
    overflow: hidden;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

/* Table View Styling */
.expanded-row {
    background-color: #f8f9fa;
}

.expanded-details {
    background-color: #f8f9fa;
}

/* Cursor Styling */
.cursor-pointer {
    cursor: pointer;
}

/* Badge Styling */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Animation for hover effects */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.product-card:hover .card-img-top {
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .product-card {
        margin-bottom: 1rem;
    }
}
