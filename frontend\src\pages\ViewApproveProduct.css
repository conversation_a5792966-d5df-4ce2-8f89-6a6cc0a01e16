/* ViewApproveProduct.css */

/* Timeline styles */
.timeline {
  position: relative;
  padding: 20px 0;
  margin: 0;
}

.timeline:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 20px;
  width: 3px;
  background-color: #e9ecef;
}

.timeline-item {
  position: relative;
  margin-bottom: 25px;
  padding-left: 45px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #007bff;
  border: 3px solid #fff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
  z-index: 1;
}

.timeline-date {
  font-weight: bold;
  margin-bottom: 8px;
  color: #495057;
}

.timeline-content {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Inventory status styles */
.inventory-status-bar {
  height: 24px;
  border-radius: 4px;
  overflow: hidden;
}

/* Card hover effects */
.card.shadow-sm {
  transition: all 0.3s ease;
}

.card.shadow-sm:hover {
  transform: translateY(-5px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Badge styles */
.badge {
  font-weight: 500;
  padding: 0.4em 0.6em;
}

/* Table hover styles */
.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}
